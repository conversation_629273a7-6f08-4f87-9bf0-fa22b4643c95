import * as cdk from 'aws-cdk-lib';
import * as opensearch from 'aws-cdk-lib/aws-opensearchservice';
import * as ec2 from 'aws-cdk-lib/aws-ec2';
import * as rds from 'aws-cdk-lib/aws-rds';
import * as elasticache from 'aws-cdk-lib/aws-elasticache';
import * as wafv2 from 'aws-cdk-lib/aws-wafv2';
import * as cognito from 'aws-cdk-lib/aws-cognito';
import * as events from 'aws-cdk-lib/aws-events';
import * as appscaling from 'aws-cdk-lib/aws-applicationautoscaling';
import * as ecr from 'aws-cdk-lib/aws-ecr';
import * as efs from 'aws-cdk-lib/aws-efs';
import { Duration } from 'aws-cdk-lib';
import { aws_s3 as s3 } from 'aws-cdk-lib';

export interface ICognitoParam {
  urlForCallback: string[];
  urlForLogout: string[];
  secretArn: string;
  identityProvider: typeof cognito.UserPoolClientIdentityProvider.COGNITO;
  stackTerminationProtection?: boolean;
}

export interface IWafParam {
  defaultAction?: wafv2.CfnWebACL.DefaultActionProperty;
  basicAuthUserName?: string;
  basicAuthUserPass?: string;
  overrideAction_CommonRuleSet?: wafv2.CfnWebACL.OverrideActionProperty;
  overrideAction_KnownBadInputsRuleSet?: wafv2.CfnWebACL.OverrideActionProperty;
  overrideAction_AmazonIpReputationList?: wafv2.CfnWebACL.OverrideActionProperty;
  overrideAction_LinuxRuleSet?: wafv2.CfnWebACL.OverrideActionProperty;
  overrideAction_SQLiRuleSet?: wafv2.CfnWebACL.OverrideActionProperty;
  overrideAction_CSCRuleSet?: wafv2.CfnWebACL.OverrideActionProperty;
  ruleAction_IPsetRuleSet?: wafv2.CfnWebACL.RuleActionProperty;
  ruleAction_BasicRuleSet?: wafv2.CfnWebACL.RuleActionProperty;
  allowIPList?: string[];
  preSharedKey?: string;
  stackTerminationProtection?: boolean;
}
export interface IOidcParam {
  OrganizationName: string;
  RepositoryNames: Record<string, string>;
  stackTerminationProtection?: boolean;
}

export interface IOpenSearchTypeParam {
  openSearchType: 'PROVISION' | 'SERVERLESS';
}

export interface IOpenSearchParam {
  openSearchProvisionedParam: {
    engineVersion: opensearch.EngineVersion;
    zoneAwareness: number;
    ebsVolumeType: ec2.EbsDeviceVolumeType;
    ebsVolumeSize: number;
    ebsIops: number;
    dataNodes: number;
    masterNodes: number;
    masterNodeInstanceType: string;
    dataNodeInstanceType: string;
  };
  openSearchServerlessParam: {
    collectionType: 'SEARCH' | 'TIMESERIES' | 'VECTORSEARCH';
  };
  stackTerminationProtection?: boolean;
}

export interface IElastiCacheParam {
  ElastiCacheSelfDesignedParam: {
    engine: 'valkey' | 'redis';
    engineVersion: string;
    numNodeGroups: number;
    replicasPerNodeGroup: number;
    minCapacity: number;
    maxCapacity: number;
    targetValueToScale: number;
    predefinedMetricToScale:
      | appscaling.PredefinedMetric.ELASTICACHE_PRIMARY_ENGINE_CPU_UTILIZATION
      | appscaling.PredefinedMetric.ELASTICACHE_REPLICA_ENGINE_CPU_UTILIZATION
      | appscaling.PredefinedMetric.ELASTICACHE_DATABASE_MEMORY_USAGE_COUNTED_FOR_EVICT_PERCENTAGE;
    enableAutoScale: boolean;
    cacheNodeTypeEnableAutoScale: string;
    cacheNodeTypeDisableAutoScale: string;
    elastiCacheCustomParam: elasticache.CfnParameterGroupProps;
  };
  ElastiCacheServerlessParam: {
    engine: 'valkey' | 'redis';
    engineVersion: string;
    dataStorageMaximum: number;
    dataStorageMinimum: number;
    ecpuPerSecondMaximum: number;
    ecpuPerSecondMinimum: number;
  };
  stackTerminationProtection?: boolean;
}

export interface IElastiCacheTypeParam {
  elastiCacheType: 'SELFDESIGNED' | 'SERVERLESS';
  stackTerminationProtection?: boolean;
}

export interface IAuroraParam {
  dbName: string;
  dbUser: string;
  dbVersion: rds.AuroraPostgresEngineVersion | rds.AuroraMysqlEngineVersion;
  writerInstanceType: ec2.InstanceType;
  /**
   * List of instance types for reader instances.
   * The number of reader instances will be determined by the array length.
   * Leave empty array [] or omit this property for Writer-only cluster.
   *
   * @example
   * // Writer-only cluster
   * readers: []
   *
   * // Multiple readers with different configurations
   * readers: [
   *   {
   *     instanceType: ec2.InstanceType.of(ec2.InstanceClass.T3, ec2.InstanceSize.MEDIUM),
   *     enablePerformanceInsights: true,
   *     autoMinorVersionUpgrade: false
   *   },
   *   {
   *     instanceType: ec2.InstanceType.of(ec2.InstanceClass.R6G, ec2.InstanceSize.LARGE),
   *     enablePerformanceInsights: false,
   *     autoMinorVersionUpgrade: true
   *   }
   * ]
   */
  readers?: Array<{
    instanceType: ec2.InstanceType;
    enablePerformanceInsights?: boolean;
    autoMinorVersionUpgrade?: boolean;
  }>;
  enablePerformanceInsights: boolean;
  removalPolicy: cdk.RemovalPolicy;
  auroraMinAcu: number;
  auroraMaxAcu: number;
  clusterParameters?: Record<string, string>;
  instanceParameters?: Record<string, string>;
  backupRetentionDays?: number;
  backupPreferredWindow?: string;
  preferredMaintenanceWindow?: string;
  autoMinorVersionUpgrade?: boolean;
  deletionProtection?: boolean;
  stackTerminationProtection?: boolean;
  /**
   * Whether to enable RDS Proxy for the Aurora cluster.
   * RDS Proxy provides connection pooling and improves scalability.
   *
   * @default false
   */
  enableRdsProxy?: boolean;
  /**
   * RDS Proxy configuration options.
   * Only used when enableRdsProxy is true.
   */
  rdsProxyConfig?: {
    /**
     * The maximum number of connections that the proxy can open to the database.
     *
     * @default 100
     */
    maxConnectionsPercent?: number;
    /**
     * The maximum percentage of idle connections allowed through the proxy.
     *
     * @default 50 (%)
     */
    maxIdleConnectionsPercent?: number;
    /**
     * Whether to require TLS for connections to the proxy.
     *
     * @default true
     */
    requireTLS?: boolean;
    /**
     * The amount of time for a connection to be idle before the proxy disconnects it.
     *
     * @default 1800 seconds (30 minutes)
     */
    idleClientTimeout?: number;
  };
}

export interface ICertificateIdentifier {
  identifier: string;
}

export type IAlbParam = ICertificateIdentifier;

export interface IBasicEcsAlbParam extends IEcrParam {
  appName: string;
  portNumber: number;
  stackTerminationProtection?: boolean;
}

export interface IOptionalEcsAlbParam extends IBasicEcsAlbParam {
  path: string;
}

export type IEcsAlbParam = [IBasicEcsAlbParam, ...IOptionalEcsAlbParam[]];

export interface IEcsParam extends IEcrParam {
  appName: string;
  portNumber: number;
}

export interface IVpcParam {
  cidr: string;
  maxAzs: number;
  natGateways: number;
  stackTerminationProtection?: boolean;
}

export interface INotifierParam {
  workspaceId: string;
  channelIdMon: string;
  monitoringNotifyEmail: string;
}

export interface ICloudFrontParam {
  fqdn: string;
  createClosedBucket: boolean;
  stackTerminationProtection?: boolean;
}

export interface IEnv {
  envName: string;
  account?: string;
  region?: string;
}

export interface IDRRegion {
  region: string;
}

export interface IBackupParam {
  backupDisasterRecovery: boolean;
  backupSchedule: events.Schedule;
  retentionPeriod: Duration;
  stackTerminationProtection?: boolean;
}

export interface IInfraResourcesPipelineParam {
  slackChannelName: string;
  slackWorkspaceId: string;
  slackChannelId: string;
  stackTerminationProtection?: boolean;
  enableNewRelic?: boolean;
}

export interface IEcrParam {
  ecrLifecycleRules?: ecr.LifecycleRule[];
}

export type IBastionParam = IEcrParam;

export interface IKmsKeyParam {
  pendingWindow: cdk.Duration;
}

export interface IRemovalPolicyParam {
  removalPolicy: cdk.RemovalPolicy;
  autoDeleteObjects: boolean;
  emptyOnDelete: boolean;
}

export const DestroyRemovalPolicyParam: IRemovalPolicyParam = {
  removalPolicy: cdk.RemovalPolicy.DESTROY,
  autoDeleteObjects: true,
  emptyOnDelete: true,
};

export const RetainRemovalPolicyParam: IRemovalPolicyParam = {
  removalPolicy: cdk.RemovalPolicy.RETAIN,
  autoDeleteObjects: false,
  emptyOnDelete: false,
};

export interface IEfsParam {
  lifecyclePolicy?: efs.LifecyclePolicy;
  outOfInfrequentAccessPolicy?: efs.OutOfInfrequentAccessPolicy;
  throughputMode: efs.ThroughputMode;
  performanceMode: efs.PerformanceMode;
  removalPolicy?: cdk.RemovalPolicy;
  isUseSharedTransferFamily?: boolean;
  sharedTransferFamilyAccountID?: string;
  hasBackup?: boolean;
  backupParams?: {
    schedule: events.Schedule;
    retentionPeriod: cdk.Duration;
    removalPolicy?: cdk.RemovalPolicy;
  };
  stackTerminationProtection?: boolean;
}

export interface ICSIRTWAFParam {
  isUseCSIRTManageRules: boolean;
  CSIRTManagerRules: {
    overrideAction: wafv2.CfnWebACL.OverrideActionProperty;
    ruleGroupArn: string;
  };
  CSIRTIpSetArn: string;
  stackTerminationProtection?: boolean;
    /**
   * Whether to enable NewRelic integration for WAF logs.
   * When enabled, WAF logs will be sent to NewRelic via Kinesis Data Firehose.
   *
   * @default false
   */
  isEnableNewRelicForWAF?: boolean;
}

export interface ISendGridLogParam {
  s3BucketArn?: string;
  suffix: string; // 4 character string
  createAlarm: boolean;
  notifyEmail: string;
  stackTerminationProtection?: boolean;
}

export interface ISendGridLog {
  stackTerminationProtection?: boolean;
}

export interface IMonitorParam {
  stackTerminationProtection?: boolean;
}
export interface IECSAppParam {
  stackTerminationProtection?: boolean;
}
export interface IShareResourcesParam {
  stackTerminationProtection?: boolean;
}

export interface SecretParam {
  stackTerminationProtection: boolean;
}

export interface INewRelicParam {
  ExternalId: string;
  stackTerminationProtection?: boolean;
}

export interface logToNewRelicSettings {
  path_pattern: string;
  api_key: string;
}

export interface ICloudfrontLogToNewRelicSetting {
  isCreate: boolean;
  newrelicSecretArn: string;
  logToNewRelicSettings: logToNewRelicSettings[];
}

export interface IWafLogToNewRelicSetting {
  isCreate: boolean;
  newrelicSecretArn: string;
  logToNewRelicSettings: logToNewRelicSettings[];
}

export interface IConfig {
  CognitoParam: ICognitoParam;
  WafParam: IWafParam;
  WafAlbParam: IWafParam;
  OidcParam: IOidcParam;
  AuroraParam: IAuroraParam;
  CertificateIdentifier: ICertificateIdentifier;
  AlbParam: IAlbParam;
  AlbBgParam: IAlbParam;
  OpensearchParam: IOpenSearchParam;
  OpensearchTypeParam: IOpenSearchTypeParam;
  ElastiCacheParam: IElastiCacheParam;
  EcsFrontTasks: IEcsAlbParam;
  EcsFrontBgTasks: IEcsAlbParam;
  EcsBackTasks: IEcsParam[];
  EcsBackBgTasks: IEcsAlbParam;
  s3AuditLogLifecycleRules: s3.LifecycleRule[];
  pipelineSourceBucketLifeCycleRules: s3.LifecycleRule[];
  VpcParam: IVpcParam;
  NotifierParam: INotifierParam;
  CloudFrontParam: ICloudFrontParam;
  CloudfrontLogToNewRelicSetting: ICloudfrontLogToNewRelicSetting;
  WafLogToNewRelicSetting: IWafLogToNewRelicSetting;
  InfraResourcesPipelineParam: IInfraResourcesPipelineParam;
  Env: IEnv;
  DRRegionParam: IDRRegion;
  BackupParam: IBackupParam;
  BastionParam?: IBastionParam;
  KmsKeyParam?: IKmsKeyParam;
  LogRemovalPolicyParam?: IRemovalPolicyParam; // For LogGroup and S3 for log
  OtherRemovalPolicyParam?: IRemovalPolicyParam; // For ECR, Pipeline, etc.
  EfsParam: IEfsParam;
  ElastiCacheTypeParam: IElastiCacheTypeParam;
  CSIRTWAFParamALB?: ICSIRTWAFParam;
  CSIRTWAFParamCF?: ICSIRTWAFParam;
  SendGridLogParams?: ISendGridLogParam[];
  SendGridLog: ISendGridLog;
  ShareResourcesParam: IShareResourcesParam;
  MonitorParam: IMonitorParam;
  ECSAppParam: IECSAppParam;
  SecretParam: SecretParam;
  NewRelicParam: INewRelicParam;
  S3BuildLogLifecycleRules: s3.LifecycleRule[];
}
