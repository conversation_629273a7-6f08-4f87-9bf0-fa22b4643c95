import * as cdk from 'aws-cdk-lib';
import { Construct } from 'constructs';
import * as lambda from 'aws-cdk-lib/aws-lambda';
import * as iam from 'aws-cdk-lib/aws-iam';
import * as logs from 'aws-cdk-lib/aws-logs';
import * as s3 from 'aws-cdk-lib/aws-s3';
import { StringParameter } from 'aws-cdk-lib/aws-ssm';
import { logToNewRelicSettings } from 'params/interface';

export interface NewRelicLogS3Props {
  prefix: string;
  removalPolicy?: cdk.RemovalPolicy;
  newrelicSecretArn: string;
  logBucket: s3.Bucket | s3.IBucket;
  layer?: lambda.ILayerVersion;
  logType: string;
  logSetting?: logToNewRelicSettings[];
  lambdaFunctionName?: string;
}

/**
 * Constructs a NewRelicLogIngestionS3Construct, which sets up a Lambda function
 * for ingesting logs from an S3 bucket and forwarding them to New Relic.
 *
 * This construct creates a Lambda function with a specified runtime, handler, and
 * environment variables. It also configures a CloudWatch Log Group for the Lambda
 * function and applies necessary IAM policies for accessing S3 and Secrets Manager.
 *
 * @extends Construct
 *
 * @param scope - The scope in which this construct is defined.
 * @param id - The unique identifier for this construct.
 * @param props - The properties for configuring the construct.
 * @param props.prefix - A prefix used for naming resources.
 * @param props.layer - (Optional) A custom Lambda layer to use. If not provided, a default layer is loaded from SSM.
 * @param props.newrelicSecretArn - The ARN of the New Relic license key stored in Secrets Manager.
 * @param props.logType - (Optional) The type of logs being ingested. Defaults to 'alb'.
 * @param props.logBucket - The S3 bucket containing the logs to be ingested.
 * @param props.removalPolicy - (Optional) The removal policy for the CloudWatch Log Group. Defaults to `DESTROY`.
 *
 * @property function - The Lambda function created by this construct.
 */
export class NewRelicLogIngestionS3Construct extends Construct {
  public readonly function: lambda.Function;
  constructor(scope: Construct, id: string, props: NewRelicLogS3Props) {
    super(scope, id);

    const lambdaLogGroup = new logs.LogGroup(this, 'FunctionLogGroup', {
      logGroupName: `/aws/lambda/${props.prefix}/NewRelicFunctionLog`,
      retention: logs.RetentionDays.ONE_YEAR,
      removalPolicy: props.removalPolicy ?? cdk.RemovalPolicy.DESTROY,
    });
    const lambdaLayers = [];

    console.log('layer arn', `/${props.prefix}/NewRelicLayerArn`);

    if (!props.layer) {
      // load default layer from SSM
      lambdaLayers.push(lambda.LayerVersion.fromLayerVersionArn(
        this,
        'NewRelicLayer',
        StringParameter.valueForStringParameter(this, `/${props.prefix}/NewRelicLayerArn`),
      ));
    } else {
      lambdaLayers.push(props.layer);
    }

    this.function = new lambda.Function(this, 'function', {
      functionName: `${props.prefix}-nr-log`,
      runtime: lambda.Runtime.PYTHON_3_12,
      layers: lambdaLayers,
      handler: `${props.lambdaFunctionName ?? 'NewRelicLogIngestionS3'}.lambda_handler`,
      code: lambda.Code.fromAsset('lambda/NewRelic'),
      timeout: cdk.Duration.seconds(900),
      logGroup: lambdaLogGroup,
      memorySize: 256,
      environment: {
        LICENSE_KEY_ARN: props.newrelicSecretArn,
        LICENSE_KEY_MAPPING: JSON.stringify(props.logSetting ?? []),
        LOG_TYPE: props.logType || 'alb',
        DEBUG_ENABLED: 'false',
      },
    });

    const lambdaPolicy = new iam.PolicyStatement({
      actions: ['s3:GetObject', 's3:ListBucket', 'secretsmanager:GetSecretValue'],
      resources: [props.newrelicSecretArn, props.logBucket.bucketArn, `${props.logBucket.bucketArn}/*`],
    });
    this.function.addToRolePolicy(lambdaPolicy);

    // Listen to S3 bucket events and trigger the Lambda function
    props.logBucket.addEventNotification(
      cdk.aws_s3.EventType.OBJECT_CREATED_PUT,
      new cdk.aws_s3_notifications.LambdaDestination(this.function),
    );
  }
}