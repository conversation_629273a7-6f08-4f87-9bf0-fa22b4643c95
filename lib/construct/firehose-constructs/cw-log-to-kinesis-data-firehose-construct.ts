import * as cdk from 'aws-cdk-lib';
import { Construct } from 'constructs';
import * as firehose from 'aws-cdk-lib/aws-kinesisfirehose';
import * as iam from 'aws-cdk-lib/aws-iam';
import * as logs from 'aws-cdk-lib/aws-logs';
import * as s3 from 'aws-cdk-lib/aws-s3';
import { DataFirehose } from './data-firehose-construct';

export interface CWLogToDataFirehoseProps {
  firehoseStreamName: string;
  firehoseLogGroupName: string;
  sourceCWLogGroup: logs.ILogGroup | logs.ILogGroup[];
  httpEndpointUrl: string;
  httpEndpointName: string;
  s3BackupMode: 'AllData' | 'FailedDataOnly';
  secretArn: string;
  logRemovalPolicyParam?: {
    removalPolicy: cdk.RemovalPolicy;
    autoDeleteObjects: boolean;
  };
  firehoseBucketLifecycleRules: s3.LifecycleRule[];
}

export class CWLogToDataFirehose extends Construct {
  constructor(scope: Construct, id: string, props: CWLogToDataFirehoseProps) {
    super(scope, id);

    const firehoseStream = new DataFirehose(this, 'Firehose', {
      firehoseStreamName: props.firehoseStreamName,
      firehoseLogGroupName: props.firehoseLogGroupName,
      httpEndpointUrl: props.httpEndpointUrl,
      httpEndpointName: props.httpEndpointName,
      s3BackupMode: props.s3BackupMode,
      secretArn: props.secretArn,
      logRemovalPolicyParam: props.logRemovalPolicyParam,
      firehoseBucketLifecycleRules: props.firehoseBucketLifecycleRules,
    });

    // Create IAM role for CloudWatch Logs to trigger Kinesis Data Firehose
    const toKinesisFirehoseRole = new iam.Role(this, 'ToKinesisFirehoseRole', {
      assumedBy: new iam.ServicePrincipal('logs.amazonaws.com'),
      managedPolicies: [iam.ManagedPolicy.fromAwsManagedPolicyName('AmazonKinesisFirehoseFullAccess')],
    });
    // Create subscription filter for CloudWatch Logs
    // check if sourceCWLogGroup is an array
    if (Array.isArray(props.sourceCWLogGroup)) {
      props.sourceCWLogGroup.forEach((logGroup, index) => {
        new logs.CfnSubscriptionFilter(this, `CfnSubscriptionFilter-${index + 1}`, {
          destinationArn: firehoseStream.stream.attrArn,
          filterPattern: '',
          logGroupName: logGroup.logGroupName,
          roleArn: toKinesisFirehoseRole.roleArn,
        });
      });
    } else {
      new logs.CfnSubscriptionFilter(this, 'CfnSubscriptionFilter', {
        destinationArn: firehoseStream.stream.attrArn,
        filterPattern: '',
        logGroupName: props.sourceCWLogGroup.logGroupName,
        roleArn: toKinesisFirehoseRole.roleArn,
      });
    }
  }
}
